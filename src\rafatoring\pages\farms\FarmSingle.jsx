import React,{useState,useEffect} from 'react'
import { MenuItem } from '@mui/material';
import ReactTooltip from 'react-tooltip';
import { Avatar, TableRow, TableCell, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../components/organisms';
import { BiDetail } from "react-icons/bi";
import { FaRegEdit } from "react-icons/fa";
import { AiOutlineUserDelete } from "react-icons/ai";
import { BsFillPersonCheckFill } from "react-icons/bs";
import { useHistory } from 'react-router-dom';
import Swal from 'sweetalert2';
import axios from 'axios';
import { URL_API } from '../../../services';
import FormsEditFazenda from '../../../components/FormsEditFazenda';

function FarmSingle({data, page, rowsPerPage, areas, refresh}) {
    
    const history = useHistory();

    const ident = window.localStorage.getItem('@Ident');

    const [open, setOpen] = useState(false);
    const [fazendaEditar, setFazendaEditar] = useState({});

    const getArea = (nomeArea) => {
        if(nomeArea){
           let areaFilter = areas.filter((area) => {
               return area.id == nomeArea
           })
           return areaFilter[0]?.nome_da_area;
        } 
        else{ 
            return '---'
        }
    }

    const handleDetails = (id) =>{
        history.push('/detalhefazenda/'+ id);
    }

    const handleEdit = (fazendaEditar) => {
        setOpen(true);
        setFazendaEditar(fazendaEditar);
    }

    const showModal=()=>{
        return (<FormsEditFazenda open={[open,setOpen]} onHide={() => setOpen(false)} fazenda={fazendaEditar} refresh={() => refresh()}/>)
    }

    const handleAtivar = (i) => {
        if(i.ativo_inativo === "INATIVO") {
          Swal.fire({
            title:'Ativar fazenda',
            text:'Deseja ativar esta fazenda ?',
            icon:'question',
            showCancelButton: true,
            cancelButtonText: 'Não',
            confirmButtonText: `Sim`,
          }).then(async (result) => {
            if (result.isConfirmed) {
              const dataToSend = {...i, ativo_inativo: "ATIVO"}
          
              const response = await axios.put(URL_API+"atualizar_fazenda", dataToSend)
              if(response.data === "OK") {
                refresh();
              }
            }
          })
        } else {
          Swal.fire({
            title:'Desativar fazenda',
            text:'Deseja desativar esta fazenda ?',
            icon:'question',
            showCancelButton: true,
            cancelButtonText: 'Não',
            confirmButtonText: `Sim`,
          }).then(async (result) => {
            if (result.isConfirmed) {
              const dataToSend = {...i, ativo_inativo: "INATIVO"}
          
              const response = await axios.put(URL_API+"atualizar_fazenda", dataToSend)
              if(response.data === "OK") {
                refresh()
              }
            }
          })
        }
      }
   
    return (
        <>
        {data?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
            <TableRow key={index}>
                <TableCell style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight:'bold'}}>{i.data_do_cadastro || '---'}</h6>
                    <h6>{i.hora_do_cadastro}</h6>
                </TableCell>
                <TableCell style={{ maxWidth:'100px'}} align='center'>
                    <h6 style={{fontWeight:'bold'}}>{i.nome_da_fazenda}</h6>
                    <h6>{i.nome_do_proprietario}</h6>
                </TableCell>
                <TableCell align='center' style={{ maxWidth:'100px'}}>
                    <h6>{getArea(i.id_area)}</h6>
                </TableCell>
                <TableCell align='center'>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.id_fzd}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{i.ativo_inativo === "ATIVO" ? "ATIVO" : "INATIVO"}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.id_fzd}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor:i.ativo_inativo==="ATIVO"?'rgba(0, 169, 86, 1)':"#ff0000ba", 
                            borderRadius:'3px',
                            marginLeft: '15px'
                        }}>
                    </div>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem onClick={() => handleDetails(i.id_fzd)}>
                            <ListItemIcon><BiDetail size={20}/></ListItemIcon>
                            Detalhes da fazenda
                        </MenuItem>
                        {ident == 'ADMINAIBA'
                            &&
                            <>
                                <MenuItem onClick={() => handleEdit(i)}>
                                    <ListItemIcon><FaRegEdit size={20}/></ListItemIcon>
                                    Editar
                                </MenuItem>
                                <MenuItem onClick={() => handleAtivar(i)}>
                                    {i.ativo_inativo == 'ATIVO'
                                        ?
                                        <>
                                            <ListItemIcon><AiOutlineUserDelete size={20}/></ListItemIcon>
                                            Inativar
                                        </>
                                        :
                                        <>
                                            <ListItemIcon><BsFillPersonCheckFill size={20}/></ListItemIcon>
                                            Ativar
                                        </>
                                    }
                                </MenuItem>
                            </>
                        }
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
        ))}
        {open == true && showModal()}
        </>
    )
}

export default FarmSingle
 