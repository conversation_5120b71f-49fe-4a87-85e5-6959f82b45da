import React,{useState,useEffect} from 'react'
import { MenuItem } from '@mui/material';
import { TableRow, TableCell, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../components/organisms';
import { FaRegEdit } from "react-icons/fa";
import FormsUpdateArea from './FormsUpdateArea';

function AreasSingle({page, rowsPerPage, areas, getAreas}) {

    const [areasEdit, setAreaEdit] = useState({});
    const [open, setOpen] = useState(false);

    const handleEdit = (data) => {
        setAreaEdit(data);
        setOpen(true);
    } 

    return (
        <>
        {areas?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
            <TableRow key={index}>
                <TableCell style={{ maxWidth:'250px'}}>
                    <h6 style={{fontWeight:'bold'}}>{i.nome_da_area}</h6>
                </TableCell>
                <TableCell style={{maxWidth:'200px'}} align='center'>
                    <h6 style={{fontWeight:'bold'}}>{i.ponto_de_referencia}</h6>
                    <h6 style={{fontSize: '14px'}}>{i.raio}KM de raio</h6>
                    <h6 style={{fontSize: '14px'}}>Lat:{i.latitude}</h6>
                    <h6 style={{fontSize: '14px'}}>Lon:{i.longitude}</h6>
                </TableCell>
                <TableCell align='center'>
                    <h6 style={{fontWeight:'bold'}}>{i.Fazenda}</h6>
                    <h6>Fazendas</h6>
                </TableCell>
                <TableCell align='center'>
                    <h6 style={{fontWeight:'bold'}}>{i.Viatura}</h6>
                    <h6>Viaturas</h6>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem onClick={() => handleEdit(i)}>
                            <ListItemIcon><FaRegEdit size={20}/></ListItemIcon>
                            Editar
                        </MenuItem>
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
        ))}
            <FormsUpdateArea
                open={open} 
                onHide={() => setOpen(false)} 
                data={areasEdit}
                update={() => getAreas()}
            />
        </>
    )
}

export default AreasSingle;
 