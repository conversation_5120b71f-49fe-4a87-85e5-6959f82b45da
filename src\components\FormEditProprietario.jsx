import React, { useState } from 'react';
import axios from 'axios';
import { URL_API } from '../services';

import { AiOutlineCloudUpload } from "react-icons/ai"

import { useDropzone } from 'react-dropzone';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner,
    Modal
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';
import { IoClose } from 'react-icons/io5';

function FormEditProprietario({open, setOpen, proprietario, refresh}){

  const [openDropzone, setOpenDropzone] = useState(false);
  const [load, setLoad] = useState(false);
  const [nome, setNome] = useState(proprietario.nome_do_proprietario);
  const [cpf, setCpf] = useState(proprietario.cpf);
  const [foto, setFoto] = useState({name:proprietario.foto_do_proprietario});
  const [telefone, setTelefone] = useState(proprietario.telefone);
  const [email, setEmail] = useState(proprietario.email);
  const [data, setData] = useState(proprietario.data_de_nascimento);

    function submit(event){    
      setLoad(true);     
      event.preventDefault();     
      axios.put(URL+'atualizar_proprietario',{
          id_prop: proprietario.id_prop,
          nome_do_proprietario: nome,
          cpf: cpf,
          email: email,
          telefone: telefone,
          data_de_nascimento: data,
          foto_do_proprietario: foto.name        
        })
      .then(res=>{
          // const data = new FormData()
          // data.append('file', foto);
          // data.append('tela', 'proprietario');
          // data.append('id', res.data.last_id)
          
          // axios.post(URL+'file/',data,{
          //     headers: {
          //     "Content-Type": "multipart/form-data",
          //     },
          // })
          // .then(res=>{
            refresh()
            setLoad(false);                  
            Swal.fire(nome,' atualizado com sucesso!','success')            
          // })
          // .catch(err=>{
          //     setLoad(false);
          //     Swal.fire('Erro','Tente novamente!','error')
          // })
      })     
      .catch(err=>{
          console.log(err)
          setLoad(false);
          Swal.fire('Erro','Tente novamente!','error')
      })     
    }  
    
    return (   
        <>
            <Modal              
                show={open}
                onHide={() => setOpen(false)}
                aria-labelledby="example-modal-sizes-title-lg"
                contentClassName='dialogModal'          
            >
                <Modal.Header>
                    <Modal.Title>
                        <h5 style={{fontWeight:'bolder'}}>Editar proprietário</h5>
                    </Modal.Title>
                    <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>setOpen(false)} style={{fontSize:25, color:'#868686'}}/></Button>
                </Modal.Header>
                <Modal.Body>
          
                    <Form className="" onSubmit={submit}>
                        <Row className="g-2">
                            <Col md>
                                <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                    <Form.Label className='customLabel'>Nome do Proprietário <font className='obr'>*</font></Form.Label>
                                    <Form.Control value={nome} className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                                </Form.Group>
                            </Col>
                            <Col md>
                                <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                    <Form.Label className='customLabel'>CPF <font className='obr'>*</font></Form.Label>
                                    <Form.Control value={cpf} className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setCpf(e.target.value)} required/>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="g-2">
                            <Col md>
                                <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                    <Form.Label className='customLabel'>E-mail <font className='obr'>*</font></Form.Label>
                                    <Form.Control value={email} className='customInput shadow' type="text" onChange={e => setEmail(e.target.value)} placeholder="Digite aqui" required/>
                                </Form.Group>
                            </Col>
                            <Col md>
                                <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                    <Form.Label className='customLabel'>Telefone <font className='obr'>*</font></Form.Label>
                                    <Form.Control value={telefone} className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setTelefone(e.target.value)} required/>
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className="g-2">
                            <Col md={6}>
                                <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                    <Form.Label className='customLabel'>Data de Nascimento <font className='obr'>*</font></Form.Label>
                                    <Form.Control value={data} className='customInput shadow' type="date" placeholder="Digite aqui" onChange={e=>setData(e.target.value)} required/>
                                </Form.Group>
                            </Col>
                            <Col md>
                                <Form.Label className='customLabel'>Foto <font className='obr'>*</font></Form.Label>
                                <br/>
                                <Button variant='outline-secondary' className='customInput shadow uploadFoto' style={{width:220}} onClick={() => setOpenDropzone(true)}>{foto ? foto.name : <div>Faça o Upload <AiOutlineCloudUpload style={{fontSize:25}}/></div>}</Button>
                                {/* <DropzoneDialog
                                    acceptedFiles={['image/*']}
                                    cancelButtonText={"cancel"}
                                    submitButtonText={"submit"}
                                    open={openDropzone}
                                    onClose={() => setOpenDropzone(false)}
                                    onSave={(files) => {
                                        setFoto(files[0])
                                        setOpenDropzone(false);
                                    }}
                                    showPreviews={true}
                                    showFileNamesInPreview={true}
                                    dropzoneText={"Clique, ou arraste e solte a imagem aqui"}
                                /> */}
                            </Col>
                        </Row>
                        <div class="col text-center mt-3">
                            <Button className='blueColor btnCadastro' type="submit" size='lg'>                                                  
                                {load 
                                    ? 
                                    <Spinner animation="border" />
                                    :
                                    'Atualizar proprietário'
                                }                              
                            </Button>
                        </div>                     
                    </Form>        
                </Modal.Body>
            </Modal>         
        </>
    );
}

export default FormEditProprietario;