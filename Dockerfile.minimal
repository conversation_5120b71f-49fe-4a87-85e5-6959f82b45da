# Emergency minimal Dockerfile for servers with severe resource constraints
# This version prioritizes stability over build speed

# Build Stage - Using the smallest possible Node image
FROM node:16-alpine AS build

# Install only git (absolutely minimal)
RUN apk add --no-cache git

WORKDIR /app

# Copy package files
COPY package*.json ./

# Ultra-conservative npm configuration
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set optional false && \
    npm config set prefer-offline true

# Extremely conservative memory settings
ENV NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"
ENV CI=true
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false

# Install dependencies with maximum constraints
RUN npm install --legacy-peer-deps --no-audit --no-fund --no-optional --maxsockets=1 --prefer-offline

COPY . .

# Accept build arguments for environment variables
ARG REACT_APP_URL
ARG REACT_APP_URL_API
ARG REACT_APP_URL_SOCKET

# Set environment variables for the build
ENV REACT_APP_URL=$REACT_APP_URL
ENV REACT_APP_URL_API=$REACT_APP_URL_API
ENV REACT_APP_URL_SOCKET=$REACT_APP_URL_SOCKET

# Build with maximum memory safety and timeout protection
RUN timeout 600 sh -c '\
    export NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size --gc-interval=100" && \
    export GENERATE_SOURCEMAP=false && \
    export INLINE_RUNTIME_CHUNK=false && \
    npm run build' || \
    (echo "Build timed out, trying with even less memory..." && \
    export NODE_OPTIONS="--max-old-space-size=768" && \
    export GENERATE_SOURCEMAP=false && \
    npm run build)

# Production Stage
FROM nginx:stable-alpine AS production

# Minimal nginx setup
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

# Simple healthcheck without additional dependencies
HEALTHCHECK --interval=60s --timeout=3s --start-period=5s --retries=2 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
