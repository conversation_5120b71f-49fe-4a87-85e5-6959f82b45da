{"name": "aiba", "version": "0.1.0", "private": true, "dependencies": {"@babel/core": "^7.16.0", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/icons-material": "^5.0.1", "@mui/material": "^5.7.0", "@react-pdf/renderer": "^2.0.19", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@tippy.js/react": "^3.1.1", "@turf/center": "^7.2.0", "@turf/circle": "^6.5.0", "@turf/helpers": "^6.5.0", "axios": "^0.21.1", "bootstrap": "^5.0.2", "date-fns": "^2.27.0", "geolib": "^3.3.1", "google-maps-react": "^2.0.6", "immer": "^9.0.21", "jspdf-react": "^1.0.11", "mapbox-gl": "^2.3.1", "moment": "^2.29.1", "ncrypt-js": "^2.0.0", "qrcode.react": "^1.0.1", "react": "^17.0.2", "react-bootstrap": "^1.6.1", "react-bootstrap-range-slider": "^3.0.2", "react-bootstrap-typeahead": "^5.2.0", "react-data-table-component": "^6.11.8", "react-dom": "^17.0.2", "react-dropzone": "^14.2.3", "react-geolocated": "^3.2.0", "react-icons": "^4.12.0", "react-input-mask": "^2.0.4", "react-map-gl": "^6.1.17", "react-native-elements": "^3.4.2", "react-native-vector-icons": "^8.1.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-tooltip": "^4.2.21", "sass": "^1.51.0", "socket.io-client": "4.2.0", "styled-components": "^5.3.0", "sweetalert2": "^11.0.18", "use-immer": "^0.6.0", "use-position": "^0.0.8", "web-vitals": "^1.0.1", "worker-loader": "^3.0.8"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:coverage": "npm test -- --env=jsdom --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"source-map-explorer": "^2.5.3", "webpack-bundle-analyzer": "^4.10.2"}}