import React,{useState,useEffect} from 'react'
import Header from '../components/Header'
import Map from '../components/Mapa'
import { TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import SingleListItem from '../components/PoliceRoutes/SingleListItem'
import {URL} from '../services/index'
import {URL_API} from '../services/index'
import Footer from '../components/Footer'
import { useHistory } from 'react-router';
// import LocalFireDepartmentIcon from '@material-ui/icons/LocalFireDepartment';

import axios from 'axios'

function PoliceRoutes () {
    const [Select, setSelect] = useState(0)
    const [state, setstate] = useState("")
    const [color, setcolor] = useState(0)
    const [selected, setselected] = useState("")
    const [search, setsearch] = useState({value:""})
    const [ID, setID] = useState(localStorage.getItem("@Ident"))
    const history = useHistory();

    useEffect(() => {
        async function fun(){
            var data=await fetch(`${URL}todas_areas`);
            data=await data.json();
            console.log("Dataaaaaaa",data)
            // data=data.sort(change)
            setstate(data)
            // setstate1(data)
        }
        fun()

    }, [])//toggle
    // useEffect(() => {
    //     console.log("ID is ",ID)
    //     if(ID==="COORDPM"){
    //         window.location.href="/initialRoute";
    //     }
    // }, [])


    const [areasAtuacao, setAreasAtuacao] = useState([])

    useEffect(()=>{
      async function fetchData(){
          const areasResponse = await axios.get(URL_API+"todas_areas")
          setAreasAtuacao(areasResponse.data)   
      }
      fetchData();
    },[]
  );
  

    return (
        <>
        <Header/>

        <div style={{display:'flex',fontFamily:'sans-serif',width:'100%',height:'700px',flexWrap:'wrap',marginTop:'60px'}}>
            <div style={{width:'45%',display:'flex',justifyContent:'flex-end'}}>
                <div style={{display:'flex',width:'500px'}}>
                    <Map areasAtuacao={areasAtuacao} height={650} largura={"100%"}/>
                </div>
            </div>   
            <div style={{fontFamily:'sans-serif',padding:'40px',width:'55%'}}>
                <div style={{display:'flex',height:'600px',flexDirection:'column',width:'600px'}}>    
                    <div style={{display:'flex',alignItems:'center',justifyContent:'space-between',width:'100%'}}>
                        <h5 style={{fontSize:'24px',fontFamily:"Trebuchet MS"}}>Escolha a área de atuação da rota</h5>
                        <TextField
                            value={search.value}
                            onChange={(e) => setsearch({ value: e.target.value })}
                            placeholder="Pesquisar..."
                            size="small"
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon />
                                    </InputAdornment>
                                ),
                            }}
                            sx={{
                                maxWidth: 200,
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: '20px',
                                    backgroundColor: '#F2F2F2',
                                }
                            }}
                        />
                    </div>
                    <div style={{marginTop:'40px',color:'#B2A9A9',paddingLeft:'15px',display:'flex',width:'600px'}}>
                        <p style={{width:'25%',fontSize:'12px'}}>Fazenda</p>
                        <p style={{width:'25%',fontSize:'12px'}}>Localização</p>
                        <p style={{width:'25%',fontSize:'12px'}}>Visitas Recebidas</p>
                        {/* <p style={{width:'25%',marginRight:'20px',fontSize:'12px'}}>Extras</p> */}
                    </div>
                    <div style={{height:'500px',overflow:'auto',width:'100%',paddingRight:"10px"}}>
                        {
                            state?state.map((e)=>(
                                <SingleListItem selected={selected} setSelect={setSelect} setselected={setselected} color={color} data={e}/>
                            )):"Loading"
                        }
                        <div style={{position:'relative',marginBottom:'20px',marginLeft:'350px'}}>
                            <button disabled={!Select} onClick={()=> history.push('/PoliceRoutes2')} style={{border:'none',width:'200px',height:'55px',backgroundColor:'#0076C1',color:'white',borderRadius:"40px"}}>
                                Prosseguir
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <Footer/>
        </>
    )
}

export default PoliceRoutes
