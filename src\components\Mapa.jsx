import React, { useState } from 'react';
import ReactTooltip from 'react-tooltip';
import { useEffect } from 'react';
import MapGL from "react-map-gl";
import {Marker, Source, Layer, Popup} from "react-map-gl";
import { useAlertContext } from '../context/alertContext';
import axios from 'axios';
import { useImmer } from 'use-immer';
import { URL_API } from '../services';
import { RiPoliceCarFill } from "react-icons/ri"
import { AiFillAlert } from "react-icons/ai"
import { GiHouse } from "react-icons/gi"
import { circle } from "@turf/circle";
import { useMapContext } from '../context/mapContext';
import mapboxgl from "mapbox-gl"; // This is a dependency of react-map-gl even if you didn't explicitly install it
import { ControlCameraOutlined } from '@mui/icons-material';
// eslint-disable-next-line import/no-webpack-loader-syntax
// mapboxgl.workerClass = require("worker-loader!mapbox-gl/dist/mapbox-gl-csp-worker").default;

const isLatitudeAndLongitudeValid = (latitude, longitude) => Number.isFinite(Number(longitude)) && Number.isFinite(Number(latitude))
  && Number(latitude) >= -90 
  && Number(latitude) <= 90
  && Number(longitude) >= -180
  && Number(longitude) <= 180

const TOKEN = 'pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueTJ3cjMxeTFzMm5tbHZsc29id2t0In0.eUpkMW39cxUGx3t747gQUA';

export default function Map({ areasAtuacao, showAreasSelect = true, policias = true, fazenda = true, ...props}) {
  const [latPopUp, setlatPopUp] = useState(0)
  const [lngPopUp, setlngPopUp] = useState(0)
  const [nomePolicia, setNomePolicia] = useState('')
  const [policiais, setPoliciais] = useState([]);
  const [fazendas, setFazendas] = useState([])
  const [showPopUp, setShowPopUp] = useState(false);
  const {datas} = useAlertContext();
  const {idArea} = useMapContext();
  const [circles, setCircles] = useState([{
    lat:Number(window.localStorage.getItem('latitude')),
    lng:Number(window.localStorage.getItem('longitude')),
    radius:0,
  }]);

  useEffect(()=>{
      async function fetchData(){
        const response = await axios.get(URL_API+"panico_last");
        setPoliciais(response.data);
        const responseFzd = await axios.get(URL_API+'todas_fazendas');
        setFazendas(responseFzd.data)
      }
      fetchData();
    },[]);

    useEffect(() => {

      if(!!areasAtuacao && !!areasAtuacao.length){
          const tempCircles = [];
          console.log("tempCircles:", tempCircles)
          for(var i = 0; i< areasAtuacao.length; i++){
            if(isLatitudeAndLongitudeValid(areasAtuacao[i].latitude, areasAtuacao[i].longitude) && areasAtuacao[i].raio){
              tempCircles.push({
                lat:areasAtuacao[i].latitude,
                lng:areasAtuacao[i].longitude,
                radius:areasAtuacao[i].raio
              })
            } 
          }
          //console.log("tempCircles:", tempCircles)
  
          if(tempCircles.length > 0) {
            setCircles(tempCircles)

            if(tempCircles[0].lat && tempCircles[0].lng){
              setViewport({
                latitude: Number(tempCircles[0].lat),
                longitude: Number(tempCircles[0].lng),
                zoom: 10,
                bearing: 0,
                pitch: 0
              })
            } 
          }
      }
    },[areasAtuacao])
    
  const [viewport, setViewport] = useState({
    latitude: (circles.length && isLatitudeAndLongitudeValid(circles[0].lat, circles[0].lng) ? circles[0].lat: Number(window.localStorage.getItem('latitude'))) , 
    longitude:(circles.length && isLatitudeAndLongitudeValid(circles[0].lat, circles[0].lng) ? circles[0].lng: Number(window.localStorage.getItem('longitude'))),
    zoom: 10,
    bearing: 0,
    pitch: 0
  })

  const handleAreaSelectChange = e => {

    const tempCircles = [];

    const index = e.target.value;
    
    if(props.setAreaSelected){
      props.setAreaSelected(e.target.value);
    }

    if(index === "todos"){
      for(var i = 0; i< areasAtuacao.length; i++){
        if(isLatitudeAndLongitudeValid(areasAtuacao[i].latitude, areasAtuacao[i].longitude) && areasAtuacao[i].raio){
          tempCircles.push({
            lat:areasAtuacao[i].latitude,
            lng:areasAtuacao[i].longitude,
            radius:areasAtuacao[i].raio
          })
        } 
      }
    }
    else{
      if(isLatitudeAndLongitudeValid(areasAtuacao[index].latitude, areasAtuacao[index].longitude) && areasAtuacao[index].raio){
        tempCircles.push({
          lat:areasAtuacao[index].latitude,
          lng:areasAtuacao[index].longitude,
          radius:areasAtuacao[index].raio
        })
      }
    }
    setCircles(tempCircles)

    if(tempCircles.length && isLatitudeAndLongitudeValid(tempCircles[0].lat, tempCircles[0].lng)){
      setViewport({
        latitude: Number(tempCircles[0].lat),
        longitude: Number(tempCircles[0].lng),
        zoom: 10,
        bearing: 0,
        pitch: 0
      })
    } 
  }

  return (
    <>
      <MapGL
        width={props.largura}
        height={props.height || 530}
        {...viewport}
        mapStyle="mapbox://styles/mapbox/streets-v11"
        mapboxApiAccessToken={TOKEN}
        onViewportChange={viewport => setViewport(viewport)}
        className='mapa'
        logoEnabled={false}
        center={
          circles.length 
          && isLatitudeAndLongitudeValid(circles[0].lat,circles[0].lng) ? [circles[0].lng, circles[0].lat] : 
          [Number(window.localStorage.getItem('longitude')),
          Number(window.localStorage.getItem('latitude'))]
        }
      >
        {showPopUp && <Popup
          latitude={latPopUp}
          longitude={lngPopUp}
          closeButton={true}
          closeOnClick={false}
          onClose={() => setShowPopUp(false)}
          anchor="top" >
          {nomePolicia !== null
            &&
            <div>Policial <b>{nomePolicia}</b> pedindo reforço!</div>
          }
          </Popup>
        }

        { 
          areasAtuacao && showAreasSelect &&
          <> 
            <select style={{width:'50%',marginLeft:'25%',marginTop:'50px'}} className='customInput shadow form-control' onChange={handleAreaSelectChange}>
              <option value={"todos"} >Selecione a área de atuação</option>
              {
                areasAtuacao.map((area, i) =><option value={i} >Área de Atuação: {area.nome_da_area} </option>)
              }
            </select>
    
          </>
        }

        {props.areaRota  && 
          <> 
            <select style={{width:'50%',marginLeft:'25%',marginTop:'50px'}} className='customInput shadow form-control'>
              <option>Área de Atuação: {props.areaRota.nome_da_area} </option>
            </select>
          </>
        }

        {datas.map(dados=>
          <Marker
            longitude={Number(dados[5])}
            latitude={Number(dados[4])}
            onClick={()=>{
              setlatPopUp(Number(dados[4]))
              setlngPopUp(Number(dados[5]))
              setShowPopUp(true)
              // if(dados[1] == 'Policial'){
                setNomePolicia(dados[0])
                // console.log(dados[0])
              // }
            }}
          >
            {dados[3] === 'acionado' 
              ? (<div className="marker temporary-marker"><span><AiFillAlert className='icon-police'/></span></div>) 
              : (<div className="marker-andamento temporary-marker"><span><AiFillAlert className='icon-police'/></span></div>)
            }
          </Marker>
        )}
        {policias
          &&
          policiais.map(dados =>
            <Marker
              longitude={Number(dados.longitude)}
              latitude={Number(dados.latitude)}
            >
              <div className="marker-police"><span><RiPoliceCarFill className='icon-police'/></span></div>
            </Marker>
          ) 
        }
        {fazenda
          &&
          fazendas.map((dados,index)=>
            isLatitudeAndLongitudeValid(Number(dados.latitude), Number(dados.longitude))
              &&
              <Marker
                longitude={Number(dados.longitude)}
                latitude={Number(dados.latitude)}
              >
                <div className="marker-police">
                  <ReactTooltip className="text" place="bottom" effect="solid" id={`Map-Icon-${index}`}>
                    <p>{dados.nome_da_fazenda}</p>
                  </ReactTooltip>
                  <span data-tip="" data-for={`Map-Icon-${index}`}
                    style={{backgroundColor: dados.ativo_inativo === 'ATIVO' ? "rgba(0, 169, 86, 1)" : "#f7d917"}}>
                    <GiHouse  className='icon-police'/>               
                  </span>               
                </div>
              </Marker> 
        )}
        {/* {
        cicles.map((data, index)=>
          <Source id={index} type="geojson" data={data[index]}>
            <Layer
              id="point-90-hi"
              type="fill"
              paint={{
                "fill-color": "#088",
                "fill-opacity": 0.4,
                "fill-outline-color": "yellow"
              }}
            />
          </Source>,
          console.log('aaa')
        )} */}

        {
          circles.map((circle,index) => {  
            try{
              const options = { steps: 50, units: "kilometers", properties: { foo: "bar" } };
              const circleData = circle([circle.lng, circle.lat], circle.radius, options);

              return (
                <Source 
                  id={`cicle${index}`} 
                  type="geojson" key={index} 
                  data={circleData}>
                  <Layer
                    id={`point-90-hi${index}`}
                    type="fill"
                    paint={{
                      "fill-color":  "#088",
                      "fill-opacity": 0.4,
                      "fill-outline-color": "yellow"
                    }}
                  />
                </Source>
              )
            }
            catch {
              return null
            }      
          })
        } 
      </MapGL>
    </>
  );
}
