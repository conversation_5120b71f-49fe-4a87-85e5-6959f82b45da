import React from 'react';
import DataTable from 'react-data-table-component';
import { Card, CardGroup } from 'react-bootstrap'
import './CardRotas.css';
import ReactTooltip from 'react-tooltip';
import HotelIcon from '@mui/icons-material/Hotel';
import WhatshotIcon from '@mui/icons-material/Whatshot';
import fzd from '../assets/img/bg-14.jpg';


class CardRotas extends React.Component {
    render() {
        return (
            <CardGroup>
                <Card className="rotas-fazendas-card">
                    <span 
                        className={`rotas-fazendas-card-tooltip ${this.props.visitado  ? "visitado" : ""}`}
                        data-tip 
                        data-for={`tooltip-${this.props.id_fzd}`}>      
                    </span>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${this.props.id_fzd}`} aria-haspopup='true' type="dark" effect='solid'>            
                    <span>{this.props.visitado ? "Visitado" : "Aguardando Visita"}</span>         
                    </ReactTooltip>

                    <Card.Img className="rotas-fazendas-img" src={fzd} />
                    <Card.Body className="fazenda-proprietario">
                        <Card.Title className="nome-da-fazenda">{this.props.nome_da_fazenda}</Card.Title>
                        <Card.Text>
                            {/* {this.props.proprietario} */}
                        </Card.Text>
                        <div className="ajusteicones">
                        {this.props.combustivel == 1 && <WhatshotIcon className="Icon_combustivel"/>} 
                        {this.props.pernoite == 1 && <HotelIcon className="Icon_pernoite"/>}
                        </div>
                    </Card.Body>
                    <Card.Body className="list-group-flush">
                        <Card.Text className="latidude">Lat: {this.props.latitude}</Card.Text>
                        <Card.Text >Long: {this.props.longitude}</Card.Text>
                    </Card.Body>
                </Card>
            </CardGroup>
        ) 
    }
}

export default CardRotas;



