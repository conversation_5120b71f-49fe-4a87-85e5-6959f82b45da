#!/bin/bash

# Optimized Docker build script for Coolify
# This script builds the Docker image with resource constraints to prevent server overload

set -e

# Configuration
IMAGE_NAME=${1:-"aiba-web"}
DOCKERFILE=${2:-"Dockerfile"}
BUILD_MEMORY=${3:-"1g"}
BUILD_CPUS=${4:-"1.0"}

echo "🚀 Starting optimized Docker build..."
echo "📦 Image name: $IMAGE_NAME"
echo "📄 Dockerfile: $DOCKERFILE"
echo "💾 Memory limit: $BUILD_MEMORY"
echo "🔧 CPU limit: $BUILD_CPUS"

# Create buildx builder with resource limits if it doesn't exist
if ! docker buildx ls | grep -q "resource-limited"; then
    echo "🔨 Creating resource-limited builder..."
    docker buildx create --name resource-limited --driver docker-container
fi

# Use the resource-limited builder
docker buildx use resource-limited

# Build with resource constraints
echo "🏗️ Building Docker image with resource constraints..."
docker buildx build \
    --platform linux/amd64 \
    --file "$DOCKERFILE" \
    --tag "$IMAGE_NAME:latest" \
    --build-arg BUILD_CONCURRENCY=1 \
    --build-arg NPM_CONFIG_JOBS=1 \
    --memory="$BUILD_MEMORY" \
    --load \
    .

echo "✅ Build completed successfully!"
echo "🏷️ Image tagged as: $IMAGE_NAME:latest"

# Optional: Show image size
echo "📊 Image size:"
docker images "$IMAGE_NAME:latest" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Cleanup builder (optional)
# docker buildx rm resource-limited

echo "🎉 Optimized build process completed!"
