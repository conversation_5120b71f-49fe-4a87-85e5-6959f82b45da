#!/bin/bash

# Test script to verify environment variables are embedded in the React build
# This script checks if the environment variables are present in the built JavaScript files

echo "🔍 Testing Environment Variables in Built Application..."

# Check if container is running
if ! docker ps | grep -q "aiba-web-env-test"; then
    echo "❌ Container aiba-web-env-test is not running"
    echo "Run: docker run -d -p 8081:80 --name aiba-web-env-test aiba-web-env-test"
    exit 1
fi

echo "✅ Container is running"

# Wait for health check
echo "⏳ Waiting for health check..."
sleep 10

# Test health endpoint
echo "🏥 Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/health)
if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo "✅ Health check passed (HTTP $HEALTH_RESPONSE)"
else
    echo "❌ Health check failed (HTTP $HEALTH_RESPONSE)"
fi

# Test main application
echo "🌐 Testing main application..."
MAIN_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/)
if [ "$MAIN_RESPONSE" = "200" ]; then
    echo "✅ Main application accessible (HTTP $MAIN_RESPONSE)"
else
    echo "❌ Main application failed (HTTP $MAIN_RESPONSE)"
fi

# Extract and check JavaScript files for environment variables
echo "🔍 Checking for environment variables in JavaScript files..."
docker exec aiba-web-env-test find /usr/share/nginx/html/static/js -name "*.js" -exec grep -l "aibabackend.agilemakers.com.br" {} \; 2>/dev/null | head -1 | while read jsfile; do
    if [ ! -z "$jsfile" ]; then
        echo "✅ Found environment variables in: $jsfile"
        docker exec aiba-web-env-test grep -o "https://aibabackend.agilemakers.com.br" "$jsfile" | head -3
    else
        echo "❌ Environment variables not found in JavaScript files"
    fi
done

echo "🎉 Environment variable test completed!"
