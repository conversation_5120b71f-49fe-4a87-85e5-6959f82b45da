import React, { lazy, Suspense } from 'react';
import { Spinner } from 'react-bootstrap';

// Lazy load the heavy map component
const Mapa = lazy(() => import('./Mapa'));

const MapLoadingSpinner = () => (
  <div className="d-flex justify-content-center align-items-center" style={{ height: '530px' }}>
    <Spinner animation="border" role="status">
      <span className="sr-only">Carregando mapa...</span>
    </Spinner>
  </div>
);

function LazyMap(props) {
  return (
    <Suspense fallback={<MapLoadingSpinner />}>
      <Mapa {...props} />
    </Suspense>
  );
}

export default LazyMap;
