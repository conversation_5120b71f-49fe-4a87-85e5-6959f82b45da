import React, {useState, useEffect} from 'react';
import { NavDropdown, Mo<PERSON>, But<PERSON>, Container, Card, ListGroup } from 'react-bootstrap';
import { IoClose } from "react-icons/io5"
import ReorderIcon from '@mui/icons-material/Reorder';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { useHistory } from 'react-router';
import CustomButtonCriar from './CustomButtonCriar';
import { URL_API } from '../services';
import axios from 'axios'

function ModalRedirecionarOcorrencia(row, fetchOcorrencias) {
    const [show, setShow] = useState(true);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);
    const [selectedId, setSelectedId] = useState(0)
    const [viaturas, setViaturas] = useState([]);


    async function fetchDataViaturas(props){
        const response = await axios.get(URL_API+"todas_viaturas")
        setViaturas(response.data)
    }

    useEffect(()=>{
        fetchDataViaturas()
    },[])

    const enviarViatura = (viatura_id) => {
        axios.post(URL_API+"enviar_viatura",{
            id_viat: viatura_id,
            id_ocorrencia: selectedId,
        })

        .then(res=>{
            handleClose()
            fetchOcorrencias()
        })
        .catch(err=>{
            
        })
    }
    
    const handleClickRedirecionar = (row) => {
        setShow(true)
        setSelectedId(row.id)
    } 
    return(
        <>
            <Modal
            show={show}
            onHide={() => handleClose()}
            aria-labelledby="example-modal-sizes-title-lg"
            contentClassName='dialogModal'
            >
            <Modal.Header >
                <Modal.Title>Redirecionar Ocorrência</Modal.Title>
                <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>handleClose(false)} style={{fontSize:25, color:'#868686'}}/></Button>
            </Modal.Header>
            <Modal.Body>
            {viaturas.map((item, index) =>(
              <Container className="g-1">
                <Card style={{ width: '100%', justifyContent:'Center', marginBottom:'20px'}}>
                  <Card.Header>Modelo:{item.modelo_viat}</Card.Header>
                  <ListGroup variant="flush">
                    <ListGroup.Item>#{item.id_viat}</ListGroup.Item>
                    <ListGroup.Item>Unidade:{item.unidade}</ListGroup.Item>
                    <ListGroup.Item>Area de Atuação:{item.area_atuacao}</ListGroup.Item>
                  </ListGroup>
                  <Button onClick={() => enviarViatura(item.id_viat)} style={{ backgroundColor:' rgba(0, 118, 193, 1)'}}>Enviar Viatura</Button>
                </Card>
              </Container>
            ))}
              
            </Modal.Body>
        </Modal>
        </>
    )
}
export default ModalRedirecionarOcorrencia