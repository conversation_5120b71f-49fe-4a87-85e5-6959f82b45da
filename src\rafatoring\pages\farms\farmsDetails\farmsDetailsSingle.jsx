import React,{useState,useEffect} from 'react'
import { MenuItem } from '@mui/material';
import ReactTooltip from 'react-tooltip';
import { Avatar, TableRow, TableCell, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../../components/organisms';


function FarmsDetailsSingle({data, page, rowsPerPage}) {

    const getStatus = (status) =>{
        switch(status){
            case 'concluida':
                return {text: 'Concluída', color:'rgba(0, 169, 86, 1)'}
            case 'aguardando':
                return {text: 'Aguardando', color: 'rgb(255 172 0)'}
            case 'iniciada':
                return {text: 'Iniciada', color: 'rgba(0, 118, 193, 1)'}
        }
    }
   
    return (
        <>
        {data?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
            <TableRow key={index}>
                <TableCell style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight:'bold'}}>{i.data_inicio || i.data_validacao}</h6>
                    <h6>{i.hora_inicio || i.hora_validacao}</h6>
                </TableCell>
                <TableCell style={{ maxWidth:'100px'}} align='center'>
                    <h6>#{i.id_rota}</h6>
                </TableCell>
                <TableCell align='center' style={{ maxWidth:'100px'}}>
                    <h6>{i.nome}</h6>
                </TableCell>
                <TableCell align='center' style={{ maxWidth:'100px'}}>
                    <h6>#{i.id_viatura}</h6>
                </TableCell>
                <TableCell align='center'>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.status_visita}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{getStatus(i.status_visita).text}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.status_visita}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor:getStatus(i.status_visita).color, 
                            borderRadius:'3px',
                            marginLeft: '15px'
                        }}>
                    </div>
                </TableCell>
                {/* <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem>
                            <ListItemIcon><FaRegEdit size={20}/></ListItemIcon>
                            Editar
                        </MenuItem>
                        <MenuItem>
                            <ListItemIcon><AiFillDelete size={20}/></ListItemIcon>
                            Excluir
                        </MenuItem>
                    </TableItemMenuMulti>
                </TableCell> */}
            </TableRow>
        ))}
        </>
    )
}

export default FarmsDetailsSingle
 