import React, { lazy, Suspense } from 'react'
import { SocketContext, socket } from './context/socketContext';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom'
import PrivateRoute from './PrivateRoute'
import { Spinner } from 'react-bootstrap'
import 'react-bootstrap-range-slider/dist/react-bootstrap-range-slider.css';

import { UserProvider } from './context/authContext';
import { AlertProvider } from './context/alertContext';
import { PositionProvider } from './context/locationContext';
import { MapProvider } from './context/mapContext';

// Eager load critical components
import NotFound from './components/NotFound'
import Login from './components/Login'

// Lazy load all other components
const Main = lazy(() => import('./containers/Main'));
const Routes = lazy(() => import('./rafatoring/pages/routes'));
const Fazendas = lazy(() => import('./rafatoring/pages/farms'));
const DetalheFazendas = lazy(() => import('./rafatoring/pages/farms/farmsDetails'));
const Areas = lazy(() => import('./rafatoring/pages/areas'));
const Cadastros = lazy(() => import('./containers/Cadastros'));
const DetalheRotas = lazy(() => import('./containers/DetalheRotas'));
const AlertDetail = lazy(() => import('./rafatoring/pages/occurrences/called/AlertDetail'));
const PropertyCadastraods = lazy(() => import('./containers/PropertyCadastraods'));
const RegisteredUsers = lazy(() => import('./rafatoring/pages/cops'));
const Test = lazy(() => import('./containers/Test'));
const Alert = lazy(() => import('./rafatoring/pages/occurrences/called'));
const PoliceRoutes = lazy(() => import('./containers/PoliceRoutes'));
const PoliceRoutes2 = lazy(() => import('./containers/PoliceRoutes2'));
const Vehicals = lazy(() => import('./rafatoring/pages/vehicals'));
const InitialRoute = lazy(() => import('./containers/InitialRoute'));
const InitialRoute2 = lazy(() => import('./containers/InitialRoute2'));
const SingleListItem = lazy(() => import('./components/InitialRoutes/SingleListItem'));
const Ocorrencias = lazy(() => import('./rafatoring/pages/occurrences/recordedOccurrences/index.jsx'));
const Terms = lazy(() => import('./containers/Terms.jsx'));
const Information = lazy(() => import('./rafatoring/pages/occurrences/informationListing'));
const Reports = lazy(() => import('./containers/Reports'));
const Called = lazy(() => import('./rafatoring/pages/occurrences/called'));

const LoadingSpinner = () => (
  <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
    <Spinner animation="border" role="status">
      <span className="sr-only">Carregando...</span>
    </Spinner>
  </div>
);

function App() {
  return (
    <SocketContext.Provider value={socket}>
      <AlertProvider>
        <UserProvider>
          <PositionProvider>
            <MapProvider>
              <Router>
                <Suspense fallback={<LoadingSpinner />}>
                  <Switch>
                  <Route exact path="/" component={Login} />
                  <Route exact path="/terms" component={Terms} />
                  <PrivateRoute exact path="/home" component={Main} />
                  <PrivateRoute exact path='/fazendas' component={Fazendas} />
                  <PrivateRoute exact path='/detalhefazenda/:id' component={DetalheFazendas} />
                  <PrivateRoute exact path='/detalherota/:id' component={DetalheRotas} />
                  <PrivateRoute exact path='/areas' component={Areas} />
                  <PrivateRoute exact path='/cadastros' component={Cadastros} />
                  <PrivateRoute exact path='/property' component={PropertyCadastraods} />
                  <PrivateRoute exact path='/registeredUsers' component={Called} />
                  <PrivateRoute exact path='/alert' component={Alert} />
                  <PrivateRoute exact path='/test' component={SingleListItem} />
                  <PrivateRoute exact path='/alertdetail/:id' component={AlertDetail} />
                  <PrivateRoute exact path='/InitialRoute2/:areaId/:rotaId/:responsavelId/:dataInicio' component={InitialRoute2} />
                  <PrivateRoute exact path='/property' component={PropertyCadastraods} />
                  <PrivateRoute exact path='/policiais' component={RegisteredUsers} />
                  <PrivateRoute exact path='/PoliceRoutes' component={PoliceRoutes} />
                  <PrivateRoute exact path='/PoliceRoutes2' component={PoliceRoutes2} />
                  <PrivateRoute exact path='/rotas' component={Routes} />
                  <PrivateRoute exact path='/InitialRoute/:areaId/:rotaId' component={InitialRoute} />
                  <PrivateRoute exact path='/viaturas' component={Vehicals} /> 
                  <PrivateRoute exact path='/ocorrencias' component={Ocorrencias} />                
                  <PrivateRoute exact path='/information' component={Information} />                
                  <PrivateRoute exact path='/reports' component={Reports} />                
                  <PrivateRoute component={NotFound} />
                </Switch>
                </Suspense>
              </Router>
            </MapProvider>
          </PositionProvider>
        </UserProvider>
      </AlertProvider>
    </SocketContext.Provider>
  );
}

export default App