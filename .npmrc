# NPM configuration for optimized builds
# Reduces CPU usage and network overhead during npm install

# Limit concurrent operations
maxsockets=1
jobs=1

# Disable unnecessary features
audit=false
fund=false
progress=false
optional=false

# Use faster registry (optional - uncomment if needed)
# registry=https://registry.npmmirror.com/

# Cache settings for better performance
cache-max=86400000
prefer-offline=true

# Reduce logging
loglevel=warn

# Disable package-lock updates during CI
package-lock=false
