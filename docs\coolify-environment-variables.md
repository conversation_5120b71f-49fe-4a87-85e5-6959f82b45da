# Coolify Environment Variables Setup

## Problem
React environment variables (`.env` file) don't work in Coolify because React embeds environment variables at **build time**, not runtime. Coolify's environment variables are only available at runtime.

## Solution

### 1. Updated Dockerfile
The Dockerfile now accepts build arguments and converts them to environment variables during the build process:

```dockerfile
# Accept build arguments for environment variables
ARG REACT_APP_URL
ARG REACT_APP_URL_API
ARG REACT_APP_URL_SOCKET

# Set environment variables for the build
ENV REACT_APP_URL=$REACT_APP_URL
ENV REACT_APP_URL_API=$REACT_APP_URL_API
ENV REACT_APP_URL_SOCKET=$REACT_APP_URL_SOCKET
```

### 2. Coolify Configuration

#### Option A: Using Build Arguments (Recommended)
In your Coolify application settings:

1. Go to **Build** tab
2. Add **Build Arguments**:
   ```
   REACT_APP_URL=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_API=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_SOCKET=https://aibabackend.agilemakers.com.br/
   ```

#### Option B: Using Environment Variables
In your Coolify application settings:

1. Go to **Environment Variables** tab
2. Add the following variables:
   ```
   REACT_APP_URL=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_API=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_SOCKET=https://aibabackend.agilemakers.com.br/
   ```
3. Make sure to check **"Available during build"** for each variable

### 3. Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_URL` | Main backend URL | `https://aibabackend.agilemakers.com.br/` |
| `REACT_APP_URL_API` | API endpoint URL | `https://aibabackend.agilemakers.com.br/` |
| `REACT_APP_URL_SOCKET` | WebSocket URL | `https://aibabackend.agilemakers.com.br/` |

### 4. How It Works

1. **Build Time**: Coolify passes environment variables as build arguments to Docker
2. **Docker Build**: The Dockerfile converts build arguments to environment variables
3. **React Build**: `react-scripts build` embeds these variables into the JavaScript bundle
4. **Runtime**: The built application uses the embedded values

### 5. Verification

To verify that environment variables are working:

1. **Check build logs** in Coolify for the ARG/ENV statements
2. **Inspect the built application** by checking the browser's Network tab
3. **Test API calls** to ensure they're hitting the correct endpoints

### 6. Troubleshooting

#### Variables Not Working
- Ensure variables start with `REACT_APP_`
- Check that variables are marked as "Available during build" in Coolify
- Verify the build logs show the ARG/ENV statements

#### Build Failing
- Check that all required variables are provided
- Ensure URLs end with `/` if expected by the application
- Verify the backend URLs are accessible

### 7. Local Development

For local development, create a `.env` file in the project root:

```bash
# Copy from .env.example
cp .env.example .env

# Edit with your local values
REACT_APP_URL=http://localhost:5000/
REACT_APP_URL_API=http://localhost:5000/
REACT_APP_URL_SOCKET=http://localhost:5000/
```

### 8. Security Notes

- Environment variables are embedded in the client-side JavaScript bundle
- **Never put sensitive data** (API keys, secrets) in `REACT_APP_*` variables
- These variables are visible to anyone who can access your website
- Use server-side environment variables for sensitive configuration

## ✅ Step-by-Step Coolify Setup

### Step 1: Configure Environment Variables in Coolify
1. Open your Coolify application
2. Go to **Environment Variables** tab
3. Add these variables and **check "Available during build"**:
   ```
   REACT_APP_URL=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_API=https://aibabackend.agilemakers.com.br/
   REACT_APP_URL_SOCKET=https://aibabackend.agilemakers.com.br/
   ```

### Step 2: Deploy
1. Trigger a new deployment in Coolify
2. Monitor the build logs for the ARG/ENV statements
3. Verify the application starts successfully

### Step 3: Verify
1. Check that your application can connect to the backend
2. Test API calls in the browser's Network tab
3. Confirm WebSocket connections are working

## 🔧 Troubleshooting

### Build Logs Show Missing Variables
**Problem**: Build logs don't show the ARG/ENV statements
**Solution**: Ensure variables are marked as "Available during build" in Coolify

### Application Still Uses Wrong URLs
**Problem**: App connects to localhost or wrong URLs
**Solution**:
1. Clear browser cache
2. Check that variables start with `REACT_APP_`
3. Verify the build completed successfully

### WebSocket Connection Fails
**Problem**: Socket.io connections fail
**Solution**: Ensure `REACT_APP_URL_SOCKET` points to the correct WebSocket endpoint

## 📋 Testing Checklist

- [ ] Environment variables added to Coolify
- [ ] Variables marked as "Available during build"
- [ ] Build completes successfully
- [ ] Health check passes
- [ ] Application loads without errors
- [ ] API calls work correctly
- [ ] WebSocket connections established
