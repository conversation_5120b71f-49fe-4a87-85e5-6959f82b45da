import * as React from 'react'
import { render} from '@testing-library/react';
import CardRotas from '../components/CardRotas';
import ReactTooltip from 'react-tooltip';
import WhatshotIcon from '@mui/icons-material/Whatshot';
import HotelIcon from '@mui/icons-material/Hotel';


describe('Verificando  O CardRotas', () => {
  it('Verificando o CardRotas', () => {
    const screen = render(<CardRotas/>);

    expect(screen).toBeTruthy();

    const tooltip = render(<ReactTooltip/>)

    expect(tooltip).toBeTruthy();

    const whatshotIcon = render(<WhatshotIcon/>)

    expect(whatshotIcon).toBeTruthy();

    const hotelIcon = render(<HotelIcon/>)

    expect(hotelIcon).toBeTruthy();
  })
})