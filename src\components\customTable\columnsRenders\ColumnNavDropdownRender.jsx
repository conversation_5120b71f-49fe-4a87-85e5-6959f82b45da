import React from 'react';
import './ColumnTitleDescriptionRender.css';
import { NavDropdown } from 'react-bootstrap';
import ReorderIcon from '@mui/icons-material/Reorder';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { useHistory } from 'react-router';


const ColumnNavDropdownRender = (id) =>{ 
  
  const history = useHistory();
  
  const handleClick = (idFazenda) =>{
    history.push('/detalhefazenda')
    window.localStorage.setItem('@IdFazenda', idFazenda)
  }
  return {         
      id: id,
      cell: (row) => {          
          return (            
            <NavDropdown title={<ReorderIcon color="disabled"/>} id="basic-nav-dropdown">                         
              <NavDropdown.Item >Ações</NavDropdown.Item>
              <NavDropdown.Divider />
              <NavDropdown.Item onClick={()=>handleClick(row[id])}>{<ArrowForwardIosIcon/>}Detalhes da Fazenda</NavDropdown.Item>
              <NavDropdown.Item>{<ArrowForwardIosIcon onClick={()=>console.log('clique')}/>}Editar</NavDropdown.Item>
              <NavDropdown.Item >{<ArrowForwardIosIcon/>}Excluir</NavDropdown.Item>
            </NavDropdown>                       
          )
      }
  }
}

export default ColumnNavDropdownRender; 




      


