# Build Stage
FROM node:16-alpine AS build

# Install build dependencies for native modules (bcrypt, etc.)
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    libc6-compat

WORKDIR /app
COPY package*.json ./

# Set Node.js options before npm install
ENV NODE_OPTIONS="--max-old-space-size=1536"

# Install dependencies with specific configurations for Alpine
RUN npm install --legacy-peer-deps --production=false

COPY . .
RUN npm run build
 
# Production Stage
FROM nginx:stable-alpine AS production

# Install wget for healthcheck
RUN apk add --no-cache wget

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=build /app/build /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]