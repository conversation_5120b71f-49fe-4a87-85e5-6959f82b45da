import React, { useState, useEffect } from 'react'
import Header from '../components/Header'
import Map from '../components/Mapa'
import { TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import SingleListItem from '../components/InitialRoutes/SingleListItem'
import { URL } from '../services/index'
import Footer from '../components/Footer'
import axios from 'axios'
import Swal from 'sweetalert2'
import { Spinner } from 'react-bootstrap'

import { URL_API } from '../services/index'
import { useParams } from 'react-router-dom'


function InitialRoute2() {

    const {areaId, rotaId, responsavelId, dataInicio} = useParams();

    console.log(areaId, rotaId, responsavelId, dataInicio);

    const [vehical, setvehical] = useState("")
    const [edit1, setedit1] = useState(localStorage.getItem("edit1"))

    const [state, setstate] = useState("")
    const [color, setcolor] = useState(0)
    const [load, setLoad] = useState(false)

    const [search, setsearch] = useState({ value: "" })
    
    function submit(event) {
        setLoad(true)
        axios.put(URL + "salvar_rota2",
            {
                id_viat: vehical,
                responsavel: responsavelId,
                data_inicio: dataInicio,
                status: "aguardando",
                id: rotaId
            }

        ).then(res => {
            console.log(res)
            setLoad(false);
            window.location.href = '/rotas'
        })
            .catch(error => console.log(error))

    }
    function submit1(event) {
        setLoad(true)
        axios.put(URL + "atualizar_rota_2",
            {
                id_viat: vehical,
                responsavel: responsavelId,
                data_inicio: dataInicio,
                status: "aguardando",
                id: rotaId
            }

        ).then(res => {
            console.log(res)
            setLoad(false);
            window.location.href = '/rotas'
        })
            .catch(error => console.log(error))

    }

    useEffect(() => {
        
        axios.post(URL + 'buscar_viatura_atuacao',{ id: areaId })
        .then(res => setstate(res.data))
        .catch(err => console.log("not found", err))
    }, [])
    
    const [areasAtuacao, setAreasAtuacao] = useState([])

    useEffect(() => {
        async function fetchData() {
            const areasResponse = await axios.get(URL_API + "todas_areas")
            setAreasAtuacao(areasResponse.data)
        }
        fetchData();
    }, []
    );

    return (
        <>
            <Header />

            <div style={{ display: 'flex', fontFamily: 'sans-serif', width: '100%', height: '700px', flexWrap: 'wrap', marginTop: '60px' }}>
                <div style={{ width: '45%', display: 'flex', justifyContent: 'flex-end' }}>
                    <div style={{ display: 'flex', width: '500px' }}>
                        <Map areasAtuacao={areasAtuacao} height={650} largura={"100%"} />
                    </div>
                </div>
                <div style={{ fontFamily: 'sans-serif', padding: '40px', width: '55%' }}>
                    <div style={{ display: 'flex', height: '600px', flexDirection: 'column', width: '600px' }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <h5 style={{ fontSize: '24px', fontFamily: "Trebuchet MS" }}>Escolha a viatura da rota</h5>
                            <TextField
                                value={search.value}
                                onChange={(e) => setsearch({ value: e.target.value })}
                                placeholder="Pesquisar..."
                                size="small"
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon />
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{
                                    maxWidth: 200,
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: '20px',
                                        backgroundColor: '#F2F2F2',
                                    }
                                }}
                            />
                        </div>
                        <div style={{ marginTop: '40px', color: '#B2A9A9', paddingLeft: '15px', display: 'flex', width: '600px' }}>
                            <p style={{ width: '25%', fontSize: '12px' }}>Viatura</p>
                            <p style={{ width: '25%', marginLeft: '20px', fontSize: '12px' }}></p>
                            <p style={{ width: '25%', fontSize: '12px' }}>Kms Rodados</p>
                            <p style={{ width: '25%', fontSize: '12px' }}>Visitas</p>
                        </div>
                        <div style={{ height: '500px', overflow: 'auto', width: '100%' }}>
                            {
                                state ? state.map((e) => (
                                    <SingleListItem vehical={[vehical, setvehical]} color={color} data={e} />
                                )) : "Loading"
                            }
                            <div style={{ position: 'sticky', bottom: 20, marginLeft: "350px" }}>
                                {!edit1?
                                    <button disabled={!vehical} onClick={submit} style={{ border: 'none', width: '200px', height: '55px', backgroundColor: '#0076C1', color: 'white', borderRadius: "40px" }}>
                                        {load
                                            ?
                                            <Spinner animation="border" />
                                            :
                                            'Iniciar Serviço'
                                        }
                                    </button>
                                    :
                                    <button disabled={!vehical} onClick={submit1} style={{ border: 'none', width: '200px', height: '55px', backgroundColor: '#0076C1', color: 'white', borderRadius: "40px" }}>
                                        {load
                                            ?
                                            <Spinner animation="border" />
                                            :
                                            'Editar Rota'
                                        }
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    )
}

export default InitialRoute2
