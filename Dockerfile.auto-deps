# Dockerfile with automatic peer dependency resolution
FROM node:16-alpine AS build

# Install git
RUN apk add --no-cache git

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm to automatically install peer dependencies
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set auto-install-peers true

# Conservative memory setting
ENV NODE_OPTIONS="--max-old-space-size=1536"
ENV GENERATE_SOURCEMAP=false
ENV CI=true

# Install with automatic peer dependency resolution
RUN npm install --legacy-peer-deps

COPY . .

# Build
RUN npm run build

# Production stage
FROM nginx:alpine AS production

COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
