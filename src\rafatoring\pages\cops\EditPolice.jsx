import React, {useContext, useEffect, useState} from 'react';
import { useUserContext } from '../../../context/authContext';
import { AiOutlineCloudUpload } from "react-icons/ai"

import axios from 'axios';

import { useDropzone } from 'react-dropzone';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner
} from 'react-bootstrap'
import { URL } from '../../../services';
import Swal from 'sweetalert2';

function FormsNewUser(props) {
    const {toggle,settoggle} = useUserContext();

    const [open, setOpen] = useState(props.open);
    const [load, setLoad] =useState(false);

    console.log(props.data)
    console.log(props.data.posto_graduacao)
    const [nome, setNome] = useState(props.data.nome);
    const [email, setEmail] = useState(props.data.email);
    const [unidade, setUnidade] = useState(props.data.unidade);
    const [status, setStatus] = useState(props.data.ativo_inativo);
    const [foto, setFoto] = useState(props.data.foto);
    const [telefone, setTelefone] = useState(props.data.telefone);
    const [coord,setCoord] = useState(props.data.coordenador);
    const [posto, setPosto] = useState(props.data.posto_graduacao);
    const [senha, setSenha] = useState(props.data.senha);
    const [matricula, setMatricula] = useState(props.data.matricula);
    const [id, setId] = useState(props.data.id);
    const { setTextHeader } = useUserContext();

    setTextHeader('Cadastrar Usuários')

    console.log(coord)
    function gerarPassword() {
        setSenha(Math.random().toString(36).slice(-10));
    }

    function submit(event){
        setLoad(true);
        event.preventDefault();
        axios.put(URL+'atualizar_policial',{

            "matricula": matricula,
            "senha": senha,
            "posto_graduacao": posto,
            "nome": nome,
            "foto": foto,
            "unidade": unidade,
            "email": email,
            "telefone": telefone,
            "coordenador": coord,
            "ativo_inativo": status,
            "id": id
                
        })
        .then(res=>{
            console.log(res)
            settoggle(!toggle)
            // setId(res.data.last_id)
            // console.log(id)
            // const data = new FormData()
            // data.append('file', foto);
            // data.append('tela', 'policial');
            // data.append('id', res.data.last_id)
            
            // axios.post(URL+'file/',data,{
            //     headers: {
            //     "Content-Type": "multipart/form-data",
            //     },
            // })
            // .then(res=> {
            //     console.log(res)
            //     setLoad(false);
            //     setMatricula('');
            //     setNome('');
            //     setPosto('');
            //     setSenha('');
            //     setStatus('');
            //     setTelefone('');
            //     setUnidade('');
                Swal.fire(nome,' cadastrado com sucesso!','success')
                setLoad(false)
                props.openModal[1](false);
            // })
            // .catch(err => {
            //     console.log(err)
            //     setLoad(false)
            //     Swal.fire('Erro','Tente novamente!','error')
            // })
        })
        .catch(err=>{
            setLoad(false)
            Swal.fire('Erro','Tente novamente!','error')
        })
    }
  
  return (
    <>

        <Form onSubmit={submit}>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Posto/Graduação <font className='obr'>*</font></Form.Label>
                        <select  className='customInput shadow form-control' value={posto} onChange={e => setPosto(e.target.value)}>
                            <option value='CEL-PM'>CEL-PM</option>
                            <option value='TC-PM'>TC-PM</option>    
                            <option value='MAJ-PM'>MAJ-PM</option>    
                            <option value='CAP-PM'>CAP-PM</option>    
                            <option value='TEN-PM'>TEN-PM</option>    
                            <option value='ASP-PM'>ASP-PM</option>    
                            <option value='SUB-PM'>SUB-PM</option>    
                            <option value='SGT-PM'>SGT-PM</option>    
                            <option value='CB-PM'>CB-PM</option>    
                            <option value='SD-PM'>SD-PM</option>    
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Nome <font className='obr'>*</font></Form.Label>
                        <Form.Control value={nome}  className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Unidade <font className='obr'>*</font></Form.Label>
                        <select className='customInput shadow form-control' value={unidade?unidade:"CPRO"} onChange={e=>setUnidade(e.target.value)}>
                            <option value='CPRO'>CPRO</option>
                            <option value='CIPT-O'>CIPT-O</option>    
                            <option value='85a. CIPM'>85a. CIPM</option>    
                            <option value='86a. CIPM'>86a. CIPM</option>    
                            <option value='CIPRv'>CIPRv</option>    
                            <option value='CIPE CERRADO'>CIPE CERRADO</option>     
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Email <font className='obr'>*</font></Form.Label>
                        <Form.Control value={email} className='customInput shadow' type="email" placeholder="Digite aqui" onChange={e=>setEmail(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Status <font className='obr'>*</font></Form.Label>
                        <select  className='customInput shadow form-control' value={status==="ATIVO"?"ATIVO":"INATIVO"} onChange={e=>setStatus(e.target.value)}>
                            <option selected value="ATIVO">ATIVO</option>
                            <option value="INATIVO">INATIVO</option>       
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Telefone <font className='obr'>*</font></Form.Label>
                        <Form.Control value={telefone} className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e=>setTelefone(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
            <Col md>
                    <Form.Label  className='customLabel'>Foto <font className='obr'>*</font></Form.Label>
                    <br/>
                    <Button variant='outline-secondary'  className='customInput shadow uploadFoto' style={{width:220}} onClick={() => setOpen(true)}>{foto ? foto.name : <div>Faça o Upload <AiOutlineCloudUpload style={{fontSize:25}}/></div>}{foto}</Button>
                    {/* <DropzoneDialog
                        acceptedFiles={['image/*']}
                        cancelButtonText={"cancel"}
                        submitButtonText={"submit"}
                        open={open}
                        onClose={() => setOpen(false)}
                        onSave={(files) => {
                            setFoto(files[0])
                            setOpen(false);
                        }}
                        showPreviews={true}
                        showFileNamesInPreview={true}
                        dropzoneText={"Clique, ou arraste e solte a imagem aqui"}
                    /> */}
                </Col>
                <Col>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Matricula <font className='obr'>*</font></Form.Label>
                        <Form.Control value={matricula} className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setMatricula(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Label className='customLabel'>Senha <font className='obr'>*</font></Form.Label>
                    <InputGroup className="mb-3"
                        className='customInput shadow-sm'>
                        <FormControl
                        
                        placeholder="Senha"
                        aria-label="Senha"
                        aria-describedby="basic-addon2"
                        value={senha}
                        style={{backgroundColor:'#fff'}}
                        className='customInput shadow'
                        readOnly
                        />
                        <Button variant="outline-secondary" id="button-addon2" className='customInput shadow-sm'onClick={gerarPassword}>
                            Gerar Senha
                        </Button>
                    </InputGroup>
                </Col>
            </Row>
            <Form.Group className="mb-3" controlId="formBasicCheckbox">
                <Form.Check 
                    type="checkbox" 
                    label="Coordenador"
                    defaultChecked={coord == "0" ? false : true}
                    onChange={() => setCoord(coord == "0" ? 1 : 0)}
                    className='mt-2'
                />
            </Form.Group>
            <div class="col text-center mt-3">
                <Button className='blueColor btnCadastro' type="submit" size='lg'>
                    {load 
                        ? 
                        <Spinner animation="border" />
                        :
                        'Editar Usuário'
                    }
                </Button>
            </div>
        </Form>
    </>
  );
}

export default FormsNewUser;