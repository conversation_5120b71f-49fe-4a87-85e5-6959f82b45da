import React ,{useState,useEffect} from 'react'
import {<PERSON><PERSON><PERSON>, <PERSON>, Row, Col, Spinner } from 'react-bootstrap';
// Replaced with MUI v5 TextField
import { useUserContext } from '../../../context/authContext';
import { FormControl, Input, InputAdornment, MenuItem, Select, TextField } from '@mui/material';
import { IoMdSearch } from 'react-icons/io';
import { AiOutlineSearch } from 'react-icons/ai';



function FilterRegister() {

    const [search, setsearch] = useState('')
    const {search1, setsearch1} = useUserContext()
    const {select, setselect} = useUserContext()

    const selectTrigger=(e)=>{
        setselect(e.target.value)
    }

    useEffect(() => {
        if(search)
{        setsearch1(search)
        }
    }, [search])

    

    return (
        <>
            <div style={{display: 'flex', gap: '30px'}}>
                    <span>
                        <FormControl fullWidth variant="standard">
                            <Select
                                value={select}
                                onChange={selectTrigger}
                            >
                                <MenuItem value="default">Selecione</MenuItem>
                                <MenuItem value="Coordenador">Coordenador</MenuItem>
                                <MenuItem value="Policial">Policial</MenuItem>
                            </Select>
                        </FormControl> 
                    </span>
                    <span>
                        <FormControl sx={{ mt: 0.4 }} variant="outlined">
                            <Input
                                variant="standard"
                                placeholder='Pesquisar'
                                size='small' 
                                value={search}
                                onChange={(value) => setsearch(value.target.value)}
                                endAdornment={
                                    <InputAdornment position='end'>
                                    <AiOutlineSearch size={20}/>
                                    </InputAdornment>
                                }
                            />
                        </FormControl>    
                    </span>
            </div>
        </>
    )
}

export default FilterRegister
