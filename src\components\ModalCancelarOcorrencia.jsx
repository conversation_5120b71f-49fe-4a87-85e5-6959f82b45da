import React, {useState, useEffect} from 'react';
import { NavDropdown, Modal, Button, Container, Card } from 'react-bootstrap';
import { IoClose } from "react-icons/io5"
import ReorderIcon from '@mui/icons-material/Reorder';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { useHistory } from 'react-router';
import CustomButtonCriar from './CustomButtonCriar';
import { URL_API } from '../services';
import axios from 'axios'
import { TextField, FormControl } from '@mui/material';
import Swal from 'sweetalert2';

function ModalCancelarOcorrencia({row, fetchOcorrencias}) {
    const [show, setShow] = useState(true);
    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);
    const [selectedId, setSelectedId] = useState(0)
    const [justificativa, setJustificativa] = useState('')

    const handleClickCancelar = (row) => {
        if(justificativa === ''){
            Swal.fire('ERRO', 'Preencha a justificativa','error')
        }
        else{
            axios.post(URL_API+'cancelar_ocorrencia', {
                id_ocorrencia: row.id,
                justificativa: justificativa
            })
            .then(res => {
                handleClose()
                fetchOcorrencias()
            })
            .catch(err => {
                
            })
        }
    }
    
    const handleClickRedirecionar = (row) => {
        setShow(true)
        setSelectedId(row.id)
    } 

    const handleJustificativaText = (e) => {
        if(e.target.value.length < 250){
            setJustificativa(e.target.value)
        }
    }

    return(
        <>
            <Modal
                show={show}
                onHide={() => handleClose()}
                aria-labelledby="example-modal-sizes-title-lg"
                contentClassName='dialogModal'
            >
            <Modal.Header>
                <Modal.Title>Cancelar Ocorrência</Modal.Title>
                <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>handleClose(false)} style={{fontSize:25, color:'#868686'}}/></Button>
            </Modal.Header>
            <Modal.Body>
            
                <FormControl fullWidth>
                    <TextField 
                        label="Justificativa"
                        value={justificativa}
                        onChange={(e) => handleJustificativaText(e)}
                        multiline
                        rows={4}
                    />
                </FormControl>
                <Container className="text-center">
                    <Button onClick={() => handleClickCancelar(row)} className="mt-4" style={{ backgroundColor:' rgba(0, 118, 193, 1)'}}>Cancelar Ocorrência</Button>
                </Container>
                  
              
            </Modal.Body>
        </Modal>
        </>
    )
}
export default ModalCancelarOcorrencia;