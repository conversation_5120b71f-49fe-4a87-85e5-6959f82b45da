import React, { useEffect, useState } from 'react';
import Header from '../../../../components/Header';
import { <PERSON><PERSON>,  <PERSON>, Col, Container, Modal, Form, Spinner, Card   } from 'react-bootstrap';
import axios from 'axios';
import { URL } from '../../../../services';
import {useParams} from 'react-router-dom';
import styled from 'styled-components';
import { TextField, FormControl } from '@mui/material';

const DetailDiv = styled.div`
  padding: 8px 32px;
  span {
    font-size: 13px;
    color: #97ADB6;
  }
  p {
    font-size: 15px;
    color: #3E4958;
  }
  hr {
    margin: 0;
  }
`

const CenterDiv = styled.div`

    
`

export default function AlertDetail(props) {
    const {id} = useParams();
    const [dataPanico, setDataPanico] = useState();
    const [dataFazenda, setDataFazenda] = useState();
    const [dataProprietario, setDataProprietario] = useState();
    const [loading, setLoading] = useState(false);
    const [resumo, setResumo] = useState('');
    const [showCancelar, setShowCancelar] = useState(false);
    const [showEncerrar, setShowEncerrar] = useState(false);
    const [statusText, setStatusText] = useState('');

    useEffect(() => {
        getDetalhes();
    }, [])

    const getDetalhes = () => {
        axios.post(URL+"detalhes_panico", {
            id: id
        }).then((res) => {
            setDataPanico(res.data.panico);
            setDataFazenda(res.data.fazenda)
            setDataProprietario(res.data.proprietario);
            setLoading(true);
            if(res.data.panico.STATUS == 0){
                setStatusText("Chamado de Pânico aguardando atendimento")
            }
            if(res.data.panico.STATUS == 1){
                setStatusText("Chamado de pânico em andamento")
            }
            if(res.data.panico.STATUS == 2){
                setStatusText("Chamado de pânico concluído")
            }
            if(res.data.panico.STATUS == 3){
                setStatusText("Chamado de pânico cancelado")
            }
        }).catch(err => {
            console.log(err);
        })
    }

    const confirmarCancelamento = () => {
        axios.post(URL+"panico_cancelada", {
            id: dataPanico.ID,
            resumo: resumo
        }).then(res => {
            window.location.reload();
        }).catch(err => {
            console.log(err)
        })
    }

    const confirmarEncerramento = () => {
        axios.post(URL+"panico_finalizado", {
            id: dataPanico.ID,
            resumo: resumo
        }).then(res => {
            window.location.reload();
        }).catch(err => {
            console.log(err)
        })
    }

    const handleCancelar = () => {
        setShowCancelar(!showCancelar);
    }

    const handleEncerrar = () => {
        setShowEncerrar(!showEncerrar);
    }

    const handleResumoText = (e) => {
        if(e.target.value.length < 250){
            setResumo(e.target.value)
        } 
        
    }

    const handleVoltar = () => {
        setShowEncerrar(false);
        setShowCancelar(false);
        setResumo('');
    }
    
    
    return(
        <>
        {dataPanico === undefined ? <></> : <><Header/>
            <Container className="mt-5">   
                <Row>
                    <Col>
                        <Card className='cardMap shadow' id='cardMap'>
                            <h5 className='mt-4 px-3 mb-3 text-dark'>Detalhes do pânico ID {dataPanico.ID}</h5>
                            <DetailDiv>
                                <span>Nome da fazenda</span>
                                <p>{dataFazenda !== undefined ? dataFazenda.nome_da_fazenda : 'NOME DA FAZENDA'}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Nome do proprietário</span>
                                <p>{dataProprietario !== undefined ? dataProprietario.NOME : 'NOME DO PROPRIETÁRIO'}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Telefone do proprietário</span>
                                <p>{dataProprietario !== undefined ? dataProprietario.TELEFONE : 'TELEFONE DO PROPRIETÁRIO'}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Horário</span>
                                <p>{dataPanico.HORA}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Latitude</span>
                                <p>{dataPanico.LATITUDE}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Longitude</span>
                                <p>{dataPanico.LONGITUDE}</p>
                                <hr />
                            </DetailDiv>
                            <DetailDiv>
                                <span>Status</span>
                                <p>{statusText}</p>
                                <hr />
                            </DetailDiv>
                            {dataPanico.STATUS == "2" || dataPanico.STATUS == 3 ? 
                            <DetailDiv>
                                <span>Resumo</span>
                                <p>{dataPanico.resumo_panico}</p>
                                <hr />
                            </DetailDiv>: 
                            <div>
                            </div>}
                            
                        </Card>
                        {(showCancelar || showEncerrar) || (dataPanico.STATUS == "2" || dataPanico.STATUS == 3) ? <></> : 
                        <Container>
                            <Row className="text-center">
                                <Col>
                                    <Button className='alert-footer' variant="danger" onClick={handleCancelar}>
                                        Cancelar pânico
                                    </Button>
                                </Col>
                                <Col>
                                    <Button className="azul-principal" onClick={handleEncerrar}>
                                        Encerrar pânico
                                    </Button>
                                </Col>
                            </Row>
                        </Container> }
                    </Col>
                </Row>

            </Container>
            </>}
            {showCancelar || showEncerrar ? <Container>
                <FormControl fullWidth className="mt-3" >
                    <TextField 
                        label="Resumo"
                        value={resumo}
                        onChange={(e) => handleResumoText(e)}
                    />

                    {showCancelar ? 
                        <Container>
                            <Row className="text-center">
                                <Col>
                                    <Button className="mt-4" variant="dark" onClick={() => handleVoltar()}>Voltar</Button>
                                </Col>
                                <Col>
                                    <Button className="mt-4 alert-footer" variant="danger" onClick={() => confirmarCancelamento()}>Cancelar pânico</Button>
                                </Col> 
                            </Row>
                        </Container>
                        : 
                        <Container>
                            <Row className="text-center">
                                <Col>
                                    <Button className="mt-4" variant="dark" onClick={() => handleVoltar()}>Voltar</Button>
                                    
                                </Col>
                                <Col>
                                    <Button className="azul-principal mt-4" onClick={() => confirmarEncerramento()}>Encerrar pânico</Button>
                                </Col>
                            </Row>
                        </Container>
                        
                        }
                </FormControl>

            </Container>: <></>}
            

        </>
    )
}