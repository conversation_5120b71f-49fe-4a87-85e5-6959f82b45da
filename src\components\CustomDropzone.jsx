import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from 'react-bootstrap';
import { AiOutlineCloudUpload } from "react-icons/ai";

function CustomDropzone({ onFileSelect, acceptedFiles = ['image/*'], buttonText = 'Faça o Upload', selectedFile }) {
  const onDrop = useCallback((acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles[0]);
    }
  }, [onFileSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFiles.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {}),
    multiple: false
  });

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      <Button 
        variant='outline-secondary' 
        className='customInput shadow uploadFoto mb-3' 
        style={{ width: 455 }}
      >
        {selectedFile ? selectedFile.name : (
          <div>
            {buttonText} <AiOutlineCloudUpload style={{ fontSize: 25 }} />
          </div>
        )}
      </Button>
      {isDragActive && (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          backgroundColor: 'rgba(0, 118, 193, 0.1)', 
          border: '2px dashed #0076C1',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '6px'
        }}>
          <p>Solte o arquivo aqui...</p>
        </div>
      )}
    </div>
  );
}

export default CustomDropzone;
