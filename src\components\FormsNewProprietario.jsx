import React, {useContext, useEffect, useState} from 'react';
import Header from '../components/Header'
import Footer from '../components/Footer'

import { AiOutlineCloudUpload } from "react-icons/ai"

import { useDropzone } from 'react-dropzone';

import axios from 'axios';


import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';
import InputMask from 'react-input-mask';
import { Block } from '@mui/icons-material';
import center from '@turf/center';

function FormsNewProprietario({closeModal}) {

    const [open, setOpen] = useState(false);
    const [load, setLoad] = useState(false);
    const [nome, setNome] = useState();
    const [cpf, setCpf] = useState();
    const [foto, setFoto] = useState();
    const [telefone, setTelefone] = useState();
    const [email, setEmail] = useState();
    const [data, setData] = useState();


    function submit(event){
        const tel = telefone.replace('(','')
        const tel2 = tel.replace(')','')
        const tel3 = tel2.replace(' ','')
        setLoad(true);
        event.preventDefault();
        axios.post(URL+'salvar_proprietario',{
            nome_do_proprietario: nome,
            cpf: cpf,
            email: email,
            telefone: telefone,
            data_de_nascimento: data,
            foto_do_proprietario: foto ? foto.name : "" 
        })
        .then(res=>{
            if(res.data.errmessage){
                setLoad(false);
                Swal.fire('Erro', res.data.errmessage,'error')
            }
            else{
                 axios.post(URL+'user_insert',{
                    ID_FAZENDA: null,
                    NOME: nome,
                    USUARIO: email,
                    SENHA: cpf,
                    EMAIL: email,
                    TELEFONE: '+55'+tel3, 
                    STATUS: 1,
                    PERMISSAO: 0,
                    IDENTIFICACAO: 'FZD'
                })
                .then(res=>{
                    setLoad(false);
                    Swal.fire(nome,' cadastrado com sucesso!','success')
                    closeModal();
                })
                .catch(res=>{
                    console.log(res)
                })
            }        
        })
        .catch(err=>{
            setLoad(false);
            Swal.fire('Erro','Tente novamente!','error')
        })
    }

  return (
    <>
            <Form className="" onSubmit={submit}>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Nome do Proprietário <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel' >CPF <font className='obr'>*</font></Form.Label><br/>
                            <InputMask style={{padding:'12px', width:'220px', outline:'0'}} className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setCpf(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>E-mail <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="email" onChange={e => setEmail(e.target.value)} placeholder="Digite aqui" required />
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Telefone <font className='obr'>*</font></Form.Label>
                            <InputMask style={{padding:'12px', width:'220px', outline:'0'}} className='customInput shadow' mask="(99) 999999999" type="name" placeholder="Digite aqui" onChange={e => setTelefone(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md={6}>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Data de Nascimento <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="date" placeholder="Digite aqui" onChange={e=>setData(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Label className='customLabel'>Foto <font className='obr'>*</font></Form.Label>
                        <br/>
                        <Button variant='outline-secondary' className='customInput shadow uploadFoto' style={{width:220}} onClick={() => setOpen(true)}>{foto ? foto.name : <div>Faça o Upload <AiOutlineCloudUpload style={{fontSize:25}}/></div>}</Button>
                        {/* <DropzoneDialog
                            acceptedFiles={['image/*']}
                            cancelButtonText={"cancel"}
                            submitButtonText={"submit"}
                            open={open}
                            onClose={() => setOpen(false)}
                            onSave={(files) => {
                                setFoto(files[0])
                                setOpen(false);
                            }}
                            showPreviews={true}
                            showFileNamesInPreview={true}
                            dropzoneText={"Clique, ou arraste e solte a imagem aqui"}
                        /> */}
                    </Col>
                </Row>
                <div class="col text-center mt-3">
                    <Button className='blueColor btnCadastro' type="submit" size='lg'>
                        {load 
                            ? 
                            <Spinner animation="border" />
                            :
                            'Criar Proprietário'
                        }
                    </Button>
                </div>
            </Form>
        </>
    );
}

export default FormsNewProprietario;