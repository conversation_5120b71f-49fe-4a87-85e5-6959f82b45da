import React,{useState} from 'react'
import { <PERSON>, Row, <PERSON>, Spin<PERSON>, But<PERSON> } from 'react-bootstrap';
import CreateOutlinedIcon from '@mui/icons-material/CreateOutlined';
import { URL_API } from '../services';
import FormEditProprietario from './FormEditProprietario';
import avatar from '../assets/img/avatar.jpg'



function OwnerCard({ data, refresh }) {

    const [open, setOpen] = useState(false);
   
    return (
        <>
            <Card style={{minWidth:"271px",maxWidth:"271px",height:'223px',borderRadius:"10px",overflow:'hidden',padding:'4px'}} className='shadow m-2 ' id='cardMap'>
                <Card.Header style={{ display: 'flex',justifyContent:'space-evenly', alignItems:'center', background:'white', height:'100px' }}>
                    <Card.Img variant="left" style={{
                            borderWidth: '0px',
                            width:"61px",
                            height:'61px'
                            //URL_API + data.foto_do_proprietario
                    }} src={avatar} />
                    <div style={{display:'flex',flexDirection:'column'}}>    
                        {data.nome_do_proprietario}  
                        <Card.Subtitle>{ data.cpf }</Card.Subtitle>
                    </div>
                </Card.Header>
                <Card.Body>                 
                    <div style={{ display: 'content', justifyContent: 'space-between',marginTop:'10px' }}>
                        <div>
                            <Card.Title style={{fontWeight:'100',color:'#8F7F93',fontSize:'14px'}}>
                                {data.email} 
                            </Card.Title>
                            <Card.Title style={{fontWeight:'100',color:'#8F7F93',fontSize:'14px'}}>
                                { data.telefone}
                            </Card.Title>
                        </div>
                        {/* <div style={{ display: 'flex', alignItems: 'center', flexDirection:'column' }} > 
                            <Card.Title className="text-success" style={{fontSize:'22px', font: 'ArialMT', fontWeight:'500', fontStyle: 'normal' }}>
                                {data.qtde_fazendas || '0'}
                            </Card.Title>
                            <Card.Subtitle style={{marginRight:''}}>fazendas</Card.Subtitle>
                        </div> */}
                    </div>
                    
                    <div style={{width: '53px', height: '33px', position: 'absolute', left: '216px', top: '188px', textAlign: 'center', backgroundColor: 'rgb(242, 242, 242)',lineHeight:'33px' }} >                         
                        <CreateOutlinedIcon onClick={()=>setOpen(true)} style={{ cursor:'pointer', fontSize: '20px', color: 'rgb(51, 51, 51)', padding: '2px' }} />                      
                    </div>
                </Card.Body>
            </Card>
            <FormEditProprietario setOpen={setOpen} open={open} proprietario={data} refresh={refresh}/>
        </>
    )
}

export default OwnerCard
