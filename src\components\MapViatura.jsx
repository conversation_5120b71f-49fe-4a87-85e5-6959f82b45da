import React, { useState } from 'react';
import { useEffect } from 'react';
import MapGL from "react-map-gl";
import {Marker, Source, Layer, Popup} from "react-map-gl";
import { useAlertContext } from '../context/alertContext';
import axios from 'axios';
import { useImmer } from 'use-immer';
import { URL_API } from '../services';
import { RiPoliceCarFill } from "react-icons/ri"
import { AiFillAlert } from "react-icons/ai"
import { GiHouse } from "react-icons/gi"
import { lineString } from "@turf/helpers";
//import { useMapContext } from '../context/mapContext';
import mapboxgl from "mapbox-gl"; // This is a dependency of react-map-gl even if you didn't explicitly install it
// eslint-disable-next-line import/no-webpack-loader-syntax
mapboxgl.workerClass = require("worker-loader!mapbox-gl/dist/mapbox-gl-csp-worker").default;

const isLatitudeAndLongitudeValid = (latitude, longitude) => Number.isFinite(Number(longitude)) && Number.isFinite(Number(latitude)) && Number(latitude) >= -90 && Number(latitude) <= 90

const TOKEN = 'pk.eyJ1Ijoiam9pY3lhbGJ1cXVlcnF1ZSIsImEiOiJja3JueTJ3cjMxeTFzMm5tbHZsc29id2t0In0.eUpkMW39cxUGx3t747gQUA';

export default function MapViatura(props) {
  const [policiais, setPoliciais] = useState([]);
  const [viewport, setViewport] = useState({})
  const [view, setView] = useState(false)
  const [line, setLine] = useState()


  useEffect(()=>{
      async function fetchData(){
        const response = await axios.get(URL_API+"panico_last");
        setPoliciais(response.data);
      }
      if(props.idViatura != undefined && props.latitude != undefined && props.longitude != undefined){
        setView(true)
        setViewport({
            latitude: Number(policiais[props.idViatura-1].latitude),
            longitude: Number(policiais[props.idViatura-1].longitude),
            zoom: 7,
            bearing: 0,
            pitch: 0
        })
        setLine(lineString([[Number(props.latitude), Number(props.longitude)], [Number(policiais[props.idViatura-1].latitude), Number(policiais[props.idViatura-1].longitude)]]))
      }
        
      fetchData();
      //getArea();
    },[props]);
    
    console.log(line)
  return (
    <>
    {view &&
    <MapGL
        width={props.largura}
        height={props.height || 530}
        {...viewport}
        mapStyle="mapbox://styles/mapbox/streets-v11"
        mapboxApiAccessToken={TOKEN}
        onViewportChange={viewport => setViewport(viewport)}
        className='mapa'
        logoEnabled={false}
    >
      
      
        <Marker
          longitude={Number(props.longitude)}
          latitude={Number(props.latitude)}
        >
            <div className="marker temporary-marker"><span><AiFillAlert className='icon-police'/></span></div>
        </Marker>

            <Marker
            longitude={Number(policiais[props.idViatura-1].longitude)}
            latitude={Number(policiais[props.idViatura-1].latitude)}
            >
                <div className="marker-police"><span><RiPoliceCarFill className='icon-police'/></span></div>
            </Marker>

        <Source id="my-data" type="geojson" data={line}>
          <Layer
            id="point-90-hi"
            type="line"
            paint={{ 'line-width': 2, 'line-color': '#007cbf', }}
          />
        </Source>
    </MapGL>
    }
    </>
  );
}
